{"name": "billing", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:staging": "vite build --mode staging", "build:dev": "vite build --mode production", "lint": "eslint .", "preview": "vite preview", "preview:staging": "vite preview --mode staging", "preview:production": "vite preview --mode production"}, "dependencies": {"@azure/openai": "^2.0.0", "@reduxjs/toolkit": "^2.7.0", "@tailwindcss/vite": "^4.1.4", "antd": "^5.24.8", "axios": "^1.9.0", "framer-motion": "^12.12.2", "i18next": "^25.0.0", "i18next-browser-languagedetector": "^8.0.5", "i18next-http-backend": "^3.0.2", "jwt-decode": "^4.0.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-i18next": "^15.4.1", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-responsive": "^10.0.1", "react-router-dom": "^7.5.1", "react-turnstile": "^1.1.4", "recharts": "^2.15.3", "styled-components": "^6.1.18"}, "devDependencies": {"@eslint/js": "^9.22.0", "@tailwindcss/postcss": "^4.1.4", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.4", "vite": "^6.3.1"}, "description": "This template provides a minimal setup to get <PERSON><PERSON> working in Vite with HMR and some ESLint rules.", "main": "eslint.config.js", "repository": {"type": "git", "url": "git+ssh://*****************/masharie25/frontend.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://bitbucket.org/masharie25/frontend/issues"}, "homepage": "https://bitbucket.org/masharie25/frontend#readme"}