# Maven
target/
!target/*.jar
.mvn/wrapper/maven-wrapper.jar

# IDE files
.idea/
.vscode/
*.iml
*.ipr
*.iws

# OS files
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp

# Git
.git/
.gitignore

# Docker
Dockerfile
.dockerignore
docker-compose*.yml

# Documentation
README.md
*.md

# Build scripts
build.sh

# Environment files
.env
.env.*

# Test files
src/test/

# Node modules (if any)
node_modules/

# Uploads directory (will be created in container)
uploads/
