import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

// Async thunk for sending messages to AI
export const sendMessage = createAsyncThunk(
  'chatbot/sendMessage',
  async ({ message, conversationId }, { rejectWithValue }) => {
    try {
      // Simulate API call - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
      
      // Mock AI response - replace with actual API call
      const responses = [
        "I understand your question. Let me help you with that.",
        "That's an interesting point. Here's what I think...",
        "Based on the information provided, I would suggest...",
        "Let me analyze this for you and provide a comprehensive answer.",
        "I can help you with that. Here are some options to consider...",
        "Thank you for your question. Here's my response...",
      ];
      
      const aiResponse = responses[Math.floor(Math.random() * responses.length)];
      
      return {
        userMessage: {
          id: Date.now(),
          text: message,
          sender: 'user',
          timestamp: new Date().toISOString(),
          type: 'text',
          conversationId
        },
        aiMessage: {
          id: Date.now() + 1,
          text: aiResponse,
          sender: 'ai',
          timestamp: new Date().toISOString(),
          type: 'text',
          conversationId
        }
      };
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to send message');
    }
  }
);

const initialState = {
  messages: [
    {
      id: 1,
      text: "Hello! I'm your AI assistant. How can I help you today?",
      sender: 'ai',
      timestamp: new Date().toISOString(),
      type: 'text'
    }
  ],
  isLoading: false,
  isTyping: false,
  error: null,
  conversationId: null,
  inputValue: ''
};

const chatbotSlice = createSlice({
  name: 'chatbot',
  initialState,
  reducers: {
    setInputValue: (state, action) => {
      state.inputValue = action.payload;
    },
    clearMessages: (state) => {
      state.messages = [
        {
          id: 1,
          text: "Hello! I'm your AI assistant. How can I help you today?",
          sender: 'ai',
          timestamp: new Date().toISOString(),
          type: 'text'
        }
      ];
      state.error = null;
    },
    setTyping: (state, action) => {
      state.isTyping = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    addMessage: (state, action) => {
      state.messages.push(action.payload);
    },
    setConversationId: (state, action) => {
      state.conversationId = action.payload;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(sendMessage.pending, (state) => {
        state.isLoading = true;
        state.isTyping = true;
        state.error = null;
      })
      .addCase(sendMessage.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isTyping = false;
        state.messages.push(action.payload.userMessage);
        state.messages.push(action.payload.aiMessage);
        state.inputValue = '';
      })
      .addCase(sendMessage.rejected, (state, action) => {
        state.isLoading = false;
        state.isTyping = false;
        state.error = action.payload || 'Failed to send message';
      });
  }
});

export const {
  setInputValue,
  clearMessages,
  setTyping,
  clearError,
  addMessage,
  setConversationId
} = chatbotSlice.actions;

export default chatbotSlice.reducer;

// Selectors
export const selectMessages = (state) => state.chatbot.messages;
export const selectIsLoading = (state) => state.chatbot.isLoading;
export const selectIsTyping = (state) => state.chatbot.isTyping;
export const selectError = (state) => state.chatbot.error;
export const selectInputValue = (state) => state.chatbot.inputValue;
export const selectConversationId = (state) => state.chatbot.conversationId;
