import axios from 'axios';
import { store } from '../redux/store';
import { clearAuth } from '../redux/Slice/authSlice';

// Create an axios instance with authentication
const axiosAuth = axios.create();

// Add a request interceptor to include the token
axiosAuth.interceptors.request.use(
  (config) => {
    const accessToken = localStorage.getItem('accessToken');
    if (accessToken) {
      config.headers.Authorization = `Bearer ${accessToken}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Add a response interceptor to handle token expiration
axiosAuth.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle 401 Unauthorized errors (expired token)
    if (error.response && error.response.status === 401) {
      console.log('Token expired or invalid, logging out...');
      store.dispatch(clearAuth());
      window.location.href = '/signin';
    }
    return Promise.reject(error);
  }
);

export default axiosAuth;