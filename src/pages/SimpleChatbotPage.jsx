import React, { useState } from 'react';
import { Layout, Card, Typography, Space, Button, Row, Col, Divider } from 'antd';
import { 
  MessageOutlined, 
  FullscreenOutlined, 
  CompressOutlined,
  MobileOutlined,
  DesktopOutlined 
} from '@ant-design/icons';
import ChatBot from '../components/Chatbot/ChatBot';

const { Content } = Layout;
const { Title, Paragraph, Text } = Typography;

const SimpleChatbotPage = () => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [viewMode, setViewMode] = useState('desktop'); // 'desktop' or 'mobile'

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const toggleViewMode = () => {
    setViewMode(viewMode === 'desktop' ? 'mobile' : 'desktop');
  };

  const getChatBotStyle = () => {
    if (isFullscreen) {
      return {
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 1000,
        height: '100vh',
        width: '100vw',
        borderRadius: 0
      };
    }

    if (viewMode === 'mobile') {
      return {
        height: 600,
        width: 375,
        maxWidth: '100%'
      };
    }

    return {
      height: 600,
      width: '100%',
      maxWidth: 800
    };
  };

  return (
    <Layout style={{ minHeight: '100vh', background: '#f5f5f5' }}>
      <Content style={{ padding: '24px' }}>
        {/* Header Section */}
        <Card style={{ marginBottom: 24 }}>
          <Title level={2}>
            <MessageOutlined style={{ marginRight: 8, color: '#1890ff' }} />
            Modern AI ChatBot Component
          </Title>
          <Paragraph>
            A clean, minimal, and production-ready AI chatbot component built with React and Ant Design.
            Features real-time messaging, typing indicators, responsive design, and easy API integration.
          </Paragraph>
          
          <Space wrap>
            <Button 
              type="primary"
              icon={isFullscreen ? <CompressOutlined /> : <FullscreenOutlined />}
              onClick={toggleFullscreen}
            >
              {isFullscreen ? 'Exit Fullscreen' : 'Fullscreen Mode'}
            </Button>
            
            <Button 
              icon={viewMode === 'desktop' ? <MobileOutlined /> : <DesktopOutlined />}
              onClick={toggleViewMode}
            >
              {viewMode === 'desktop' ? 'Mobile View' : 'Desktop View'}
            </Button>
          </Space>
        </Card>

        {/* Features Grid */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={12} lg={6}>
            <Card size="small">
              <Title level={4}>🎨 Modern UI</Title>
              <Text type="secondary">
                Clean design with shadows, rounded corners, and smooth animations
              </Text>
            </Card>
          </Col>
          
          <Col xs={24} sm={12} lg={6}>
            <Card size="small">
              <Title level={4}>📱 Responsive</Title>
              <Text type="secondary">
                Works perfectly on desktop, tablet, and mobile devices
              </Text>
            </Card>
          </Col>
          
          <Col xs={24} sm={12} lg={6}>
            <Card size="small">
              <Title level={4}>⚡ Real-time</Title>
              <Text type="secondary">
                Instant messaging with typing indicators and auto-scroll
              </Text>
            </Card>
          </Col>
          
          <Col xs={24} sm={12} lg={6}>
            <Card size="small">
              <Title level={4}>🔌 Easy API</Title>
              <Text type="secondary">
                Simple integration with any AI backend service
              </Text>
            </Card>
          </Col>
        </Row>

        {/* Usage Example */}
        <Card title="Usage Example" style={{ marginBottom: 24 }}>
          <Paragraph>
            <Text code>
              {`import ChatBot from './components/Chatbot/ChatBot';

function App() {
  return (
    <ChatBot 
      title="My AI Assistant"
      height={600}
      width="100%"
    />
  );
}`}
            </Text>
          </Paragraph>
        </Card>

        {/* ChatBot Demo */}
        <Card 
          title={
            <Space>
              <Text strong>Live Demo</Text>
              <Text type="secondary">({viewMode} view)</Text>
            </Space>
          }
          style={{ 
            display: 'flex', 
            flexDirection: 'column',
            alignItems: 'center'
          }}
          bodyStyle={{ 
            width: '100%', 
            display: 'flex', 
            justifyContent: 'center',
            padding: viewMode === 'mobile' ? '24px 12px' : '24px'
          }}
        >
          <ChatBot 
            title="AI Assistant Demo"
            style={getChatBotStyle()}
          />
        </Card>

        {/* API Integration Guide */}
        <Card title="API Integration" style={{ marginTop: 24 }}>
          <Title level={4}>Replace the dummy API function:</Title>
          <Paragraph>
            <Text code>
              {`// In ChatBot.jsx, replace this function:
const sendMessageToAI = async (message) => {
  const response = await fetch('/api/chat', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ message })
  });
  
  const data = await response.json();
  return data.response;
};`}
            </Text>
          </Paragraph>
          
          <Divider />
          
          <Title level={4}>Key Features:</Title>
          <ul>
            <li><Text strong>Modular Components:</Text> ChatHeader, ChatBody, ChatInput, Message, TypingIndicator</li>
            <li><Text strong>Responsive Design:</Text> Mobile-first approach with breakpoints</li>
            <li><Text strong>Accessibility:</Text> ARIA labels, keyboard navigation, high contrast support</li>
            <li><Text strong>Error Handling:</Text> Graceful error handling with Ant Design messages</li>
            <li><Text strong>Auto-scroll:</Text> Smooth scrolling to new messages</li>
            <li><Text strong>Typing Indicator:</Text> Visual feedback when AI is responding</li>
            <li><Text strong>Message Timestamps:</Text> Formatted time display</li>
            <li><Text strong>Enter/Shift+Enter:</Text> Send message or new line</li>
          </ul>
        </Card>
      </Content>
    </Layout>
  );
};

export default SimpleChatbotPage;
