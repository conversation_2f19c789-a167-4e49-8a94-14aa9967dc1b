import React from 'react';
import { Layout, Card, Typography, Space, Row, Col, But<PERSON>, Divider } from 'antd';
import { 
  MessageOutlined, 
  RobotOutlined, 
  MobileOutlined,
  DesktopOutlined,
  CheckCircleOutlined 
} from '@ant-design/icons';
import FloatingChatWidget from '../components/FloatingChatWidget/FloatingChatWidget';

const { Content } = Layout;
const { Title, Paragraph, Text } = Typography;

const FloatingChatDemo = () => {
  return (
    <Layout style={{ minHeight: '100vh', background: '#f5f5f5' }}>
      <Content style={{ padding: '24px' }}>
        {/* Header Section */}
        <Card style={{ marginBottom: 24, textAlign: 'center' }}>
          <Title level={1}>
            <MessageOutlined style={{ marginRight: 12, color: '#9e3ca2' }} />
            Floating Chat Widget Demo
          </Title>
          <Paragraph style={{ fontSize: '16px', maxWidth: '800px', margin: '0 auto' }}>
            Experience our floating chat widget with Bond AI. The widget appears in the bottom-right 
            corner of every page, providing instant access to AI assistance without interrupting 
            your workflow.
          </Paragraph>
          
          <Space size="large" style={{ marginTop: 16 }}>
            <Text strong style={{ color: '#9e3ca2' }}>
              <RobotOutlined /> Bond AI Powered
            </Text>
            <Text strong style={{ color: '#51ae52' }}>
              <CheckCircleOutlined /> Always Available
            </Text>
            <Text strong style={{ color: '#1890ff' }}>
              <MobileOutlined /> Mobile Friendly
            </Text>
          </Space>
        </Card>

        {/* Features Grid */}
        <Row gutter={[24, 24]} style={{ marginBottom: 32 }}>
          <Col xs={24} md={8}>
            <Card 
              title="🎯 Smart Positioning" 
              size="small"
              style={{ height: '100%' }}
            >
              <ul style={{ paddingLeft: 16, margin: 0 }}>
                <li>Fixed bottom-right corner placement</li>
                <li>24px spacing from screen edges</li>
                <li>Responsive positioning on mobile</li>
                <li>Non-intrusive design</li>
              </ul>
            </Card>
          </Col>
          
          <Col xs={24} md={8}>
            <Card 
              title="✨ Smooth Animations" 
              size="small"
              style={{ height: '100%' }}
            >
              <ul style={{ paddingLeft: 16, margin: 0 }}>
                <li>Slide-up animation on open</li>
                <li>Fade transitions for smooth UX</li>
                <li>Pulse effect for notifications</li>
                <li>Hover effects and feedback</li>
              </ul>
            </Card>
          </Col>
          
          <Col xs={24} md={8}>
            <Card 
              title="🎨 Theme Integration" 
              size="small"
              style={{ height: '100%' }}
            >
              <ul style={{ paddingLeft: 16, margin: 0 }}>
                <li>Uses brand colors (#9e3ca2)</li>
                <li>Consistent with Ant Design theme</li>
                <li>Accessible design patterns</li>
                <li>High contrast support</li>
              </ul>
            </Card>
          </Col>
        </Row>

        {/* Instructions */}
        <Card title="How to Use" style={{ marginBottom: 24 }}>
          <Row gutter={[24, 16]}>
            <Col xs={24} md={12}>
              <Title level={4}>Desktop Experience</Title>
              <ol style={{ paddingLeft: 20 }}>
                <li>Look for the purple chat icon in the bottom-right corner</li>
                <li>Click the icon to open the chat window</li>
                <li>The chat window slides up smoothly above the icon</li>
                <li>Use the close button (X) in the header to minimize</li>
                <li>Press Escape key to quickly close the chat</li>
              </ol>
            </Col>
            
            <Col xs={24} md={12}>
              <Title level={4}>Mobile Experience</Title>
              <ol style={{ paddingLeft: 20 }}>
                <li>Tap the floating chat button</li>
                <li>Chat opens in a centered modal overlay</li>
                <li>Tap outside the chat to close</li>
                <li>Optimized for touch interactions</li>
                <li>Prevents body scroll when open</li>
              </ol>
            </Col>
          </Row>
        </Card>

        {/* Technical Details */}
        <Card title="Technical Implementation" style={{ marginBottom: 24 }}>
          <Row gutter={[24, 16]}>
            <Col xs={24} md={6}>
              <Title level={5}>Positioning</Title>
              <Text code>position: fixed</Text><br />
              <Text code>bottom: 24px</Text><br />
              <Text code>right: 24px</Text><br />
              <Text code>z-index: 1000</Text>
            </Col>
            
            <Col xs={24} md={6}>
              <Title level={5}>Animations</Title>
              <Text code>CSS transitions</Text><br />
              <Text code>transform: scale()</Text><br />
              <Text code>opacity changes</Text><br />
              <Text code>cubic-bezier easing</Text>
            </Col>
            
            <Col xs={24} md={6}>
              <Title level={5}>Accessibility</Title>
              <Text code>ARIA labels</Text><br />
              <Text code>Keyboard navigation</Text><br />
              <Text code>Screen reader support</Text><br />
              <Text code>Focus management</Text>
            </Col>
            
            <Col xs={24} md={6}>
              <Title level={5}>Responsive</Title>
              <Text code>Mobile breakpoints</Text><br />
              <Text code>Touch-friendly sizing</Text><br />
              <Text code>Overlay on mobile</Text><br />
              <Text code>Adaptive positioning</Text>
            </Col>
          </Row>
        </Card>

        {/* Integration Code */}
        <Card title="Integration Code">
          <Title level={4}>Add to your App.jsx:</Title>
          <Paragraph>
            <Text code style={{ display: 'block', padding: '12px', background: '#f6f8fa' }}>
{`import FloatingChatWidget from './components/FloatingChatWidget/FloatingChatWidget';

function App() {
  return (
    <>
      {/* Your existing routes and components */}
      <Routes>
        {/* ... your routes ... */}
      </Routes>
      
      {/* Floating Chat Widget - Available on all pages */}
      <FloatingChatWidget title="Bond AI" />
    </>
  );
}`}
            </Text>
          </Paragraph>
          
          <Divider />
          
          <Title level={4}>Customization Options:</Title>
          <Paragraph>
            <Text code style={{ display: 'block', padding: '12px', background: '#f6f8fa' }}>
{`<FloatingChatWidget 
  title="Your AI Assistant"
  position={{ bottom: 20, right: 20 }}
  showBadge={true}
  badgeCount={3}
  disabled={false}
/>`}
            </Text>
          </Paragraph>
        </Card>

        {/* Call to Action */}
        <Card style={{ textAlign: 'center', marginTop: 32 }}>
          <Title level={3}>Try the Floating Chat Widget!</Title>
          <Paragraph>
            The floating chat widget is now active on this page. Look for the purple chat icon 
            in the bottom-right corner and click it to start chatting with Bond AI.
          </Paragraph>
          
          <Space>
            <Button type="primary" size="large" icon={<MessageOutlined />}>
              Look for the Chat Icon →
            </Button>
          </Space>
        </Card>
      </Content>
      
      {/* The FloatingChatWidget is automatically included from App.jsx */}
    </Layout>
  );
};

export default FloatingChatDemo;
