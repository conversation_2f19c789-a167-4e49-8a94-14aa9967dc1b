# Floating Chat Widget

A modern, accessible floating chat widget that provides instant access to Bond AI from any page in your application.

## 🚀 Features

### ✨ **Floating Action Button (FAB)**
- **Fixed Positioning**: Bottom-right corner (24px from edges)
- **Circular Design**: Modern FAB with shadow and hover effects
- **Theme Integration**: Uses brand colors (#9e3ca2 primary, #51ae52 secondary)
- **Pulse Animation**: Subtle attention-grabbing animation for new messages
- **Accessibility**: Full ARIA labels and keyboard navigation support

### 🎬 **Smooth Animations**
- **Slide-up Animation**: Chat window slides up from bottom-right
- **Fade Transitions**: Smooth opacity changes with cubic-bezier easing
- **Scale Effects**: Gentle scale animations for open/close states
- **Hover Effects**: Interactive feedback on button hover
- **Reduced Motion**: Respects user's motion preferences

### 📱 **Responsive Design**
- **Desktop**: Chat window positioned above the FAB
- **Mobile**: Centered modal overlay with backdrop
- **Touch-Friendly**: Optimized button sizes for mobile
- **Adaptive Positioning**: Adjusts based on screen size
- **Body Scroll Prevention**: Prevents background scrolling on mobile

### 🎨 **Brand Integration**
- **Bond AI Branding**: Customized title and messaging
- **Theme Colors**: Consistent with Ant Design theme
- **Visual Hierarchy**: Clear distinction between states
- **Professional Design**: Modern, clean aesthetic

## 📦 Component Structure

```
FloatingChatWidget/
├── FloatingChatWidget.jsx    # Main component
├── FloatingChatWidget.css    # Styling and animations
├── README.md                 # This documentation
└── index.js                  # Export file
```

## 🎯 Usage

### Basic Implementation

```jsx
import FloatingChatWidget from './components/FloatingChatWidget/FloatingChatWidget';

function App() {
  return (
    <>
      {/* Your app content */}
      <Routes>
        {/* Your routes */}
      </Routes>
      
      {/* Floating Chat Widget - Available on all pages */}
      <FloatingChatWidget title="Bond AI" />
    </>
  );
}
```

### Advanced Configuration

```jsx
<FloatingChatWidget 
  title="Custom AI Assistant"
  position={{ bottom: 20, right: 20 }}
  showBadge={true}
  badgeCount={3}
  disabled={false}
/>
```

## 🔧 Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `position` | `object` | `{ bottom: 24, right: 24 }` | Position from screen edges |
| `showBadge` | `boolean` | `false` | Show notification badge |
| `badgeCount` | `number` | `0` | Number to display in badge |
| `disabled` | `boolean` | `false` | Disable the chat widget |
| `title` | `string` | `"Bond AI"` | Chat window title |

## 🎨 Styling

### CSS Classes

```css
.floating-chat-container     /* Main container */
.floating-chat-button        /* FAB button */
.floating-chat-window        /* Chat window */
.floating-chat-overlay       /* Mobile overlay */
.floating-chatbot           /* ChatBot styling */
```

### Theme Customization

The widget uses CSS custom properties that can be overridden:

```css
.floating-chat-button {
  --primary-color: #9e3ca2;
  --secondary-color: #51ae52;
  --shadow-color: rgba(158, 60, 162, 0.4);
}
```

## 📱 Responsive Behavior

### Desktop (> 768px)
- FAB positioned in bottom-right corner
- Chat window slides up above the button
- Hover effects and smooth transitions
- Escape key closes the chat

### Mobile (≤ 768px)
- Smaller FAB size (48px vs 56px)
- Chat opens as centered modal
- Backdrop overlay with blur effect
- Touch-outside-to-close functionality
- Prevents body scrolling when open

## ♿ Accessibility Features

### Keyboard Navigation
- **Tab Navigation**: Full keyboard accessibility
- **Escape Key**: Quick close functionality
- **Enter/Space**: Activate chat button
- **Focus Management**: Proper focus indicators

### Screen Reader Support
- **ARIA Labels**: Descriptive labels for all interactive elements
- **Live Regions**: Announces state changes
- **Semantic HTML**: Proper heading structure
- **Role Attributes**: Clear element purposes

### Visual Accessibility
- **High Contrast**: Support for high contrast mode
- **Focus Indicators**: Clear focus outlines
- **Color Independence**: Not relying solely on color
- **Text Alternatives**: Icons have text alternatives

## 🔄 State Management

### Internal State
```javascript
const [isOpen, setIsOpen] = useState(false);        // Chat window visibility
const [isVisible] = useState(true);                 // Widget visibility
const [hasNewMessage, setHasNewMessage] = useState(false); // Notification state
```

### Event Handlers
- `handleToggle()` - Opens/closes chat window
- `handleClose()` - Closes chat window
- `handleMinimize()` - Minimizes chat (same as close)
- `handleEscapeKey()` - Keyboard close handler
- `handleClickOutside()` - Mobile outside click handler

## 🎭 Animations

### CSS Transitions
```css
/* Smooth button transitions */
.floating-chat-button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Chat window slide animation */
.floating-chat-window {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: bottom right;
}

/* Pulse animation for notifications */
@keyframes pulseGlow {
  0% { box-shadow: 0 4px 12px rgba(158, 60, 162, 0.4); }
  50% { box-shadow: 0 4px 20px rgba(158, 60, 162, 0.8); }
  100% { box-shadow: 0 4px 12px rgba(158, 60, 162, 0.4); }
}
```

## 🔌 Integration with ChatBot

The widget uses the existing `ChatBot` component:

```jsx
<ChatBot 
  title={title}
  height={500}
  width={350}
  onMinimize={handleMinimize}
  className="floating-chatbot"
/>
```

## 🧪 Testing

### Manual Testing Checklist
- [ ] FAB appears in bottom-right corner
- [ ] Click opens chat window smoothly
- [ ] Close button works in chat header
- [ ] Escape key closes chat
- [ ] Mobile overlay works correctly
- [ ] Touch-outside closes on mobile
- [ ] Keyboard navigation works
- [ ] Screen reader announces changes
- [ ] Hover effects work on desktop
- [ ] Animations are smooth

### Automated Testing
```javascript
describe('FloatingChatWidget', () => {
  test('renders FAB button', () => {
    // Test implementation
  });

  test('opens chat on click', () => {
    // Test implementation
  });

  test('closes on escape key', () => {
    // Test implementation
  });

  test('is accessible', () => {
    // Test implementation
  });
});
```

## 🌐 Browser Support

- **Chrome**: 70+
- **Firefox**: 65+
- **Safari**: 12+
- **Edge**: 79+
- **Mobile Safari**: iOS 12+
- **Chrome Mobile**: Android 7+

## 🚀 Performance

### Optimizations
- **CSS Transforms**: Hardware-accelerated animations
- **Event Delegation**: Efficient event handling
- **Conditional Rendering**: Only renders when needed
- **Memory Management**: Proper cleanup of event listeners

### Bundle Impact
- **Component Size**: ~8KB (minified)
- **CSS Size**: ~4KB (minified)
- **Dependencies**: Uses existing Ant Design components
- **Tree Shaking**: Supports tree shaking for unused code

## 🔮 Future Enhancements

### Planned Features
- [ ] **Notification System**: Real-time message notifications
- [ ] **Position Options**: Left/right positioning options
- [ ] **Custom Themes**: Multiple color theme support
- [ ] **Sound Notifications**: Audio alerts for new messages
- [ ] **Minimized State**: Collapsed but visible state
- [ ] **Multi-language**: Internationalization support

### API Integrations
- [ ] **WebSocket Support**: Real-time message updates
- [ ] **Push Notifications**: Browser notification API
- [ ] **Analytics**: Usage tracking and metrics
- [ ] **A/B Testing**: Different widget variations

## 📄 License

This component is part of your project and follows your project's license.

## 🤝 Contributing

1. Follow existing code style and patterns
2. Add tests for new features
3. Update documentation
4. Ensure accessibility compliance
5. Test on multiple devices and browsers
