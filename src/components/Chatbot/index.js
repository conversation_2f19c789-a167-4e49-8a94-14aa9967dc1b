// Redux-based chatbot (complex version with state management)
export { default as Chatbot } from './Chatbot';
export { default as Cha<PERSON>Header } from './ChatHeader';
export { default as MessageBubble } from './MessageBubble';
export { default as MessageInput } from './MessageInput';
export { default as TypingIndicator } from './TypingIndicator';

// Simple standalone chatbot (minimal version - recommended)
export { default as ChatBot } from './ChatBot';

// Demo components
export { default as ChatbotDemo } from './ChatbotDemo';
