/* ChatBot Container */
.chatbot-container {
  border-radius: 12px !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
  overflow: hidden !important;
  max-width: 100%;
  margin: 0 auto;
}

/* Chat Header */
.chat-header {
  background: linear-gradient(135deg, #9e3ca2 0%, #b84bb8 100%);
  padding: 16px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.chat-header .ant-btn:hover {
  background: rgba(255, 255, 255, 0.1) !important;
}

/* Chat Body */
.chat-body {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  background: #fafafa;
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-height: 0; /* Important for flex scrolling */
}

.chat-body::-webkit-scrollbar {
  width: 6px;
}

.chat-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.chat-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.chat-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Welcome Message */
.welcome-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 40px 20px;
  color: #8c8c8c;
}

/* Message Styles */
.message {
  display: flex;
  margin-bottom: 12px;
  animation: messageSlideIn 0.3s ease-out;
}

.message.user-message {
  justify-content: flex-end;
}

.message.ai-message {
  justify-content: flex-start;
}

.message-content {
  max-width: 70%;
  display: flex;
  flex-direction: column;
}

.message.user-message .message-content {
  align-items: flex-end;
}

.message.ai-message .message-content {
  align-items: flex-start;
}

.message-bubble {
  padding: 12px 16px;
  max-width: 100%;
  border-radius: 18px;
  word-wrap: break-word;
  position: relative;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.user-message .message-bubble {
  background: #9e3ca2;
  color: white !important;
  
  border-bottom-right-radius: 6px;
}

.ai-message .message-bubble {
  background: white;
  color: #262626;
  border: 1px solid #e8e8e8;
  border-bottom-left-radius: 6px;
}

.message-timestamp {
  font-size: 11px !important;
  margin-top: 4px;
  opacity: 0.7;
}

/* Typing Indicator */
.typing-indicator {
  background: white !important;
  border: 1px solid #e8e8e8 !important;
  color: #8c8c8c !important;
}

.typing-indicator .ant-spin {
  color: #9e3ca2;
}

/* Modern Chat Input */
.modern-chat-input {
  padding: 20px;
  background: white;
  border-top: 1px solid #f0f0f0;
}

.input-container {
  display: flex;
  align-items: flex-end;
  gap: 12px;
  background: #f8f9fa;
  border-radius: 24px;
  padding: 8px 8px 8px 20px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.input-container:focus-within {
  border-color: #9e3ca2;
  background: white;
  box-shadow: 0 4px 16px rgba(158, 60, 162, 0.1);
}

.modern-textarea {
  flex: 1;
  border: none !important;
  background: transparent !important;
  box-shadow: none !important;
  padding: 8px 0 !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  resize: none !important;
}

.modern-textarea:focus {
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
}

.modern-textarea::placeholder {
  color: #8c8c8c;
  font-style: italic;
}

.modern-send-button {
  width: 40px !important;
  height: 40px !important;
  border-radius: 50% !important;
  background: linear-gradient(135deg, #9e3ca2 0%, #b84bb8 100%) !important;
  border: none !important;
  box-shadow: 0 2px 8px rgba(158, 60, 162, 0.3) !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 16px !important;
}

.modern-send-button:hover {
  background: linear-gradient(135deg, #8a3491 0%, #a63ba6 100%) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(158, 60, 162, 0.4) !important;
}

.modern-send-button:active {
  transform: translateY(0) !important;
}

.modern-send-button:disabled {
  background: #d9d9d9 !important;
  transform: none !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.modern-send-button:disabled:hover {
  background: #d9d9d9 !important;
  transform: none !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Streaming cursor animation */
.streaming-cursor {
  display: inline-block;
  width: 8px;
  height: 16px;
  background-color: #9e3ca2;
  margin-left: 2px;
  animation: blink 1s infinite;
  vertical-align: middle;
}

@keyframes blink {
  0% { opacity: 1; }
  50% { opacity: 0; }
  100% { opacity: 1; }
}

/* Animations */
@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .chatbot-container {
    border-radius: 0 !important;
    height: 100vh !important;
    box-shadow: none !important;
  }

  .chat-header {
    padding: 12px 16px;
  }

  .chat-body {
    padding: 12px;
  }

  .message-content {
    max-width: 85%;
  }

  .message-bubble {
    padding: 10px 14px;
    font-size: 14px;
  }

  .modern-chat-input {
    padding: 16px;
  }

  .input-container {
    padding: 6px 6px 6px 16px;
  }

  .modern-send-button {
    width: 36px !important;
    height: 36px !important;
    font-size: 14px !important;
  }

  .chat-logo-container {
    width: 36px;
    height: 36px;
  }

  .welcome-logo-container {
    width: 64px;
    height: 64px;
    padding: 8px;
  }

  .welcome-message {
    padding: 30px 16px;
  }
}

@media (max-width: 480px) {
  .message-content {
    max-width: 90%;
  }

  .chat-header .ant-typography {
    font-size: 14px;
  }

  .message-bubble {
    padding: 8px 12px;
    font-size: 13px;
  }
}

/* Accessibility */
.chatbot-container:focus-within {
  outline: 2px solid #9e3ca2;
  outline-offset: 2px;
}

.message-bubble:focus {
  outline: 1px solid #9e3ca2;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .message-bubble {
    border: 2px solid;
  }
  
  .user-message .message-bubble {
    border-color: #ffffff;
  }
  
  .ai-message .message-bubble {
    border-color: #000000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .message {
    animation: none;
  }
  
  .chat-body {
    scroll-behavior: auto;
  }
}
