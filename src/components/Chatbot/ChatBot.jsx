import React, { useState, useEffect, useRef } from 'react';
import { 
  Card, 
  Input, 
  Button, 
  Avatar, 
  Typography, 
  Space, 
  Spin, 
  message as antMessage,
  Divider 
} from 'antd';
import { 
  SendOutlined, 
  UserOutlined, 
  RobotOutlined, 
  ClearOutlined,
  LoadingOutlined 
} from '@ant-design/icons';
import './ChatBot.css';

const { TextArea } = Input;
const { Text } = Typography;

// Dummy API function - replace with your actual API
const sendMessageToAI = async (message) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
  
  // Mock responses
  const responses = [
    "I understand your question. Let me help you with that.",
    "That's an interesting point. Here's what I think...",
    "Based on the information provided, I would suggest...",
    "Let me analyze this for you and provide a comprehensive answer.",
    "I can help you with that. Here are some options to consider...",
    "Thank you for your question. Here's my response...",
  ];
  
  return responses[Math.floor(Math.random() * responses.length)];
};

// Chat Header Component
const ChatHeader = ({ onClear, title = "AI Assistant" }) => (
  <div className="chat-header">
    <Space>
      <Avatar icon={<RobotOutlined />} style={{ backgroundColor: '#1890ff' }} />
      <div>
        <Text strong style={{ color: 'white' }}>{title}</Text>
        <br />
        <Text style={{ color: 'rgba(255,255,255,0.8)', fontSize: '12px' }}>
          Online • Ready to help
        </Text>
      </div>
    </Space>
    <Button 
      type="text" 
      icon={<ClearOutlined />} 
      onClick={onClear}
      style={{ color: 'white' }}
      title="Clear conversation"
    />
  </div>
);

// Message Component
const Message = ({ message, isUser, timestamp }) => (
  <div className={`message ${isUser ? 'user-message' : 'ai-message'}`}>
    <div className="message-content">
      <div className="message-bubble">
        <Text>{message}</Text>
      </div>
      <Text className="message-timestamp" type="secondary">
        {new Date(timestamp).toLocaleTimeString([], { 
          hour: '2-digit', 
          minute: '2-digit' 
        })}
      </Text>
    </div>
  </div>
);

// Typing Indicator Component
const TypingIndicator = ({ show }) => {
  if (!show) return null;
  
  return (
    <div className="message ai-message">
      <div className="message-content">
        <div className="message-bubble typing-indicator">
          <Space>
            <Spin indicator={<LoadingOutlined style={{ fontSize: 14 }} spin />} />
            <Text type="secondary">AI is typing...</Text>
          </Space>
        </div>
      </div>
    </div>
  );
};

// Chat Body Component
const ChatBody = ({ messages, isTyping }) => {
  const messagesEndRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, isTyping]);

  return (
    <div className="chat-body">
      {messages.length === 0 && (
        <div className="welcome-message">
          <Avatar 
            size={64} 
            icon={<RobotOutlined />} 
            style={{ backgroundColor: '#1890ff', marginBottom: 16 }} 
          />
          <Text type="secondary">
            Hello! I'm your AI assistant. How can I help you today?
          </Text>
        </div>
      )}
      
      {messages.map((msg) => (
        <Message
          key={msg.id}
          message={msg.text}
          isUser={msg.isUser}
          timestamp={msg.timestamp}
        />
      ))}
      
      <TypingIndicator show={isTyping} />
      <div ref={messagesEndRef} />
    </div>
  );
};

// Chat Input Component
const ChatInput = ({ onSendMessage, disabled }) => {
  const [inputValue, setInputValue] = useState('');

  const handleSend = () => {
    if (inputValue.trim() && !disabled) {
      onSendMessage(inputValue.trim());
      setInputValue('');
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <div className="chat-input">
      <Space.Compact style={{ width: '100%' }}>
        <TextArea
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder="Type your message... (Enter to send, Shift+Enter for new line)"
          autoSize={{ minRows: 1, maxRows: 4 }}
          disabled={disabled}
          style={{ resize: 'none' }}
        />
        <Button
          type="primary"
          icon={<SendOutlined />}
          onClick={handleSend}
          disabled={!inputValue.trim() || disabled}
          loading={disabled}
        >
          Send
        </Button>
      </Space.Compact>
    </div>
  );
};

// Main ChatBot Component
const ChatBot = ({ 
  style = {}, 
  className = '',
  title = "AI Assistant",
  height = 600,
  width = '100%'
}) => {
  const [messages, setMessages] = useState([]);
  const [isTyping, setIsTyping] = useState(false);

  const handleSendMessage = async (messageText) => {
    // Add user message
    const userMessage = {
      id: Date.now(),
      text: messageText,
      isUser: true,
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);
    setIsTyping(true);

    try {
      // Call AI API
      const aiResponse = await sendMessageToAI(messageText);
      
      // Add AI response
      const aiMessage = {
        id: Date.now() + 1,
        text: aiResponse,
        isUser: false,
        timestamp: new Date().toISOString()
      };

      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error('Error sending message:', error);
      antMessage.error('Failed to send message. Please try again.');
    } finally {
      setIsTyping(false);
    }
  };

  const handleClearChat = () => {
    setMessages([]);
    antMessage.success('Conversation cleared');
  };

  return (
    <Card
      className={`chatbot-container ${className}`}
      style={{ 
        height, 
        width, 
        display: 'flex', 
        flexDirection: 'column',
        ...style 
      }}
      bodyStyle={{ 
        padding: 0, 
        height: '100%', 
        display: 'flex', 
        flexDirection: 'column' 
      }}
    >
      <ChatHeader onClear={handleClearChat} title={title} />
      <ChatBody messages={messages} isTyping={isTyping} />
      <Divider style={{ margin: 0 }} />
      <ChatInput 
        onSendMessage={handleSendMessage} 
        disabled={isTyping} 
      />
    </Card>
  );
};

export default ChatBot;
