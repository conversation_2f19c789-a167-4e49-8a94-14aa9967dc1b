# Modern AI ChatBot Component

A minimal, clean, and production-ready AI chatbot UI component built with React and Ant Design.

## 🚀 Features

### ✨ Modern UI Design
- **Clean Interface**: Modern chat panel with borders, shadows, and rounded corners
- **Message Bubbles**: Distinct styles for user (blue) and AI (white) messages
- **Responsive Layout**: Works seamlessly on desktop, tablet, and mobile
- **Smooth Animations**: Message slide-in animations and smooth scrolling

### 💬 Chat Interface
- **Auto-scroll**: Automatically scrolls to the bottom when new messages arrive
- **Typing Indicator**: Shows "AI is typing..." with loading spinner
- **Timestamps**: Displays formatted time for each message
- **Welcome Message**: Friendly greeting when chat is empty

### ⌨️ Input Features
- **Multi-line Support**: Uses Ant Design TextArea with auto-resize
- **Smart Controls**: Enter to send, Shift+Enter for new lines
- **Send Button**: Disabled when input is empty or AI is typing
- **Loading States**: Visual feedback during message sending

### 🔧 Technical Excellence
- **Functional Components**: Modern React with hooks
- **Clean Architecture**: Separated concerns (UI, state, API)
- **Error Handling**: Graceful error handling with Ant Design messages
- **Accessibility**: ARIA labels, keyboard navigation, high contrast support
- **Performance**: Optimized rendering and smooth animations

## 📦 Component Structure

```
ChatBot.jsx
├── ChatHeader      - Header with title and clear button
├── ChatBody        - Scrollable message container
│   ├── Message     - Individual message bubble
│   └── TypingIndicator - AI typing animation
└── ChatInput       - Input area with send button
```

## 🎯 Usage

### Basic Usage

```jsx
import ChatBot from './components/Chatbot/ChatBot';

function App() {
  return (
    <ChatBot 
      title="AI Assistant"
      height={600}
      width="100%"
    />
  );
}
```

### Advanced Usage

```jsx
import ChatBot from './components/Chatbot/ChatBot';

function ChatPage() {
  return (
    <div style={{ padding: '20px' }}>
      <ChatBot 
        title="Customer Support Bot"
        height={700}
        width={800}
        className="custom-chatbot"
        style={{ margin: '0 auto' }}
      />
    </div>
  );
}
```

## 🔌 API Integration

Replace the dummy `sendMessageToAI` function with your actual API:

```javascript
const sendMessageToAI = async (message) => {
  try {
    const response = await fetch('/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}` // if needed
      },
      body: JSON.stringify({ 
        message,
        conversationId: 'user-123' // if needed
      })
    });

    if (!response.ok) {
      throw new Error('Failed to send message');
    }

    const data = await response.json();
    return data.response; // or data.message, depending on your API
  } catch (error) {
    console.error('API Error:', error);
    throw error;
  }
};
```

## 🎨 Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `title` | `string` | `"AI Assistant"` | Header title text |
| `height` | `number` | `600` | Component height in pixels |
| `width` | `string\|number` | `"100%"` | Component width |
| `className` | `string` | `""` | Additional CSS class |
| `style` | `object` | `{}` | Inline styles |

## 🎨 Customization

### Styling

The component uses CSS classes that can be customized:

```css
/* Main container */
.chatbot-container { }

/* Header */
.chat-header { }

/* Message area */
.chat-body { }

/* Individual messages */
.message.user-message .message-bubble { }
.message.ai-message .message-bubble { }

/* Input area */
.chat-input { }
```

### Theme Integration

The component uses Ant Design's theme system. Customize colors in your theme:

```javascript
// In your Ant Design theme config
{
  token: {
    colorPrimary: '#your-brand-color',
    borderRadius: 12,
    // ... other tokens
  }
}
```

## 📱 Responsive Design

The component automatically adapts to different screen sizes:

- **Desktop**: Full-width with optimal padding
- **Tablet**: Adjusted spacing and message width
- **Mobile**: Compact layout with touch-friendly controls

## ♿ Accessibility

- **Keyboard Navigation**: Full keyboard support
- **Screen Readers**: ARIA labels and semantic HTML
- **High Contrast**: Support for high contrast mode
- **Focus Management**: Proper focus indicators
- **Reduced Motion**: Respects user motion preferences

## 🔄 State Management

The component manages its own state internally:

```javascript
const [messages, setMessages] = useState([]);
const [isTyping, setIsTyping] = useState(false);
```

### Message Structure

```javascript
{
  id: number,           // Unique identifier
  text: string,         // Message content
  isUser: boolean,      // true for user, false for AI
  timestamp: string     // ISO timestamp
}
```

## 🚀 Performance

- **Optimized Rendering**: Efficient re-renders with React keys
- **Smooth Scrolling**: Hardware-accelerated animations
- **Memory Efficient**: No memory leaks with proper cleanup
- **Bundle Size**: Minimal dependencies

## 🧪 Testing

The component is designed to be easily testable:

```javascript
// Example test structure
describe('ChatBot', () => {
  test('renders welcome message', () => {
    // Test implementation
  });

  test('sends message on enter key', () => {
    // Test implementation
  });

  test('shows typing indicator', () => {
    // Test implementation
  });
});
```

## 🌐 Browser Support

- Chrome 70+
- Firefox 65+
- Safari 12+
- Edge 79+

## 🔮 Future Enhancements

Potential features for future versions:

- [ ] File upload support
- [ ] Voice message recording
- [ ] Rich text formatting (markdown)
- [ ] Message reactions
- [ ] Conversation history persistence
- [ ] Multiple conversation threads
- [ ] Bot suggestions/quick replies
- [ ] Message search functionality
- [ ] Export conversation feature

## 📄 License

This component is part of your project and follows your project's license.

## 🤝 Contributing

1. Follow the existing code style
2. Add tests for new features
3. Update documentation
4. Ensure accessibility compliance

## 📞 Support

For issues or questions about this component, please refer to your project's support channels.
