/* ChatBot Container */
.chatbot-container {
  border-radius: 12px !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
  overflow: hidden !important;
  max-width: 100%;
  margin: 0 auto;
}

/* Chat Header */
.chat-header {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  padding: 16px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.chat-header .ant-btn:hover {
  background: rgba(255, 255, 255, 0.1) !important;
}

/* Chat Body */
.chat-body {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  background: #fafafa;
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-height: 0; /* Important for flex scrolling */
}

.chat-body::-webkit-scrollbar {
  width: 6px;
}

.chat-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.chat-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.chat-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Welcome Message */
.welcome-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 40px 20px;
  color: #8c8c8c;
}

/* Message Styles */
.message {
  display: flex;
  margin-bottom: 12px;
  animation: messageSlideIn 0.3s ease-out;
}

.message.user-message {
  justify-content: flex-end;
}

.message.ai-message {
  justify-content: flex-start;
}

.message-content {
  max-width: 70%;
  display: flex;
  flex-direction: column;
}

.message.user-message .message-content {
  align-items: flex-end;
}

.message.ai-message .message-content {
  align-items: flex-start;
}

.message-bubble {
  padding: 12px 16px;
  border-radius: 18px;
  word-wrap: break-word;
  position: relative;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.user-message .message-bubble {
  background: #1890ff;
  color: white;
  border-bottom-right-radius: 6px;
}

.ai-message .message-bubble {
  background: white;
  color: #262626;
  border: 1px solid #e8e8e8;
  border-bottom-left-radius: 6px;
}

.message-timestamp {
  font-size: 11px !important;
  margin-top: 4px;
  opacity: 0.7;
}

/* Typing Indicator */
.typing-indicator {
  background: white !important;
  border: 1px solid #e8e8e8 !important;
  color: #8c8c8c !important;
}

.typing-indicator .ant-spin {
  color: #1890ff;
}

/* Chat Input */
.chat-input {
  padding: 16px 20px;
  background: white;
  border-top: 1px solid #f0f0f0;
}

.chat-input .ant-input {
  border-radius: 20px !important;
  padding: 8px 16px !important;
  border: 1px solid #d9d9d9 !important;
  transition: all 0.2s ease !important;
}

.chat-input .ant-input:focus {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1) !important;
}

.chat-input .ant-btn {
  border-radius: 20px !important;
  height: auto !important;
  padding: 8px 20px !important;
  display: flex !important;
  align-items: center !important;
  gap: 6px !important;
}

/* Animations */
@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .chatbot-container {
    border-radius: 0 !important;
    height: 100vh !important;
    box-shadow: none !important;
  }

  .chat-header {
    padding: 12px 16px;
  }

  .chat-body {
    padding: 12px;
  }

  .message-content {
    max-width: 85%;
  }

  .message-bubble {
    padding: 10px 14px;
    font-size: 14px;
  }

  .chat-input {
    padding: 12px 16px;
  }

  .welcome-message {
    padding: 30px 16px;
  }
}

@media (max-width: 480px) {
  .message-content {
    max-width: 90%;
  }

  .chat-header .ant-typography {
    font-size: 14px;
  }

  .message-bubble {
    padding: 8px 12px;
    font-size: 13px;
  }
}

/* Accessibility */
.chatbot-container:focus-within {
  outline: 2px solid #1890ff;
  outline-offset: 2px;
}

.message-bubble:focus {
  outline: 1px solid #1890ff;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .message-bubble {
    border: 2px solid;
  }
  
  .user-message .message-bubble {
    border-color: #ffffff;
  }
  
  .ai-message .message-bubble {
    border-color: #000000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .message {
    animation: none;
  }
  
  .chat-body {
    scroll-behavior: auto;
  }
}
