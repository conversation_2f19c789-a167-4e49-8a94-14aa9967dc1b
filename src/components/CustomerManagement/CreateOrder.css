.main-layout {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e2e8f0;
}

.breadcrumb {
  font-size: 14px;
  font-weight: 500;
}

.breadcrumb-purple {
  color: #9e3ca2;
}

.breadcrumb-black {
  color: #1e293b;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.products-selected {
  font-size: 14px;
  color: #64748b;
}

.submit-order-btn {
  background-color: #9e3ca2;
  border: 1px solid #9e3ca2;
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.submit-order-btn:hover {
  background-color: #16a34a;
  border-color: #16a34a;
}

/* UPDATED: Grid layout for proper alignment */
.content-layout {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 30px;
  align-items: start;
}

.left-column {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* UPDATED: Section title styling to match second image */
.section-title {
  font-size: 24px;
  font-weight: 700;
  color: #000000;
  margin: 0 0 20px 0;
}

/* UPDATED: Product box styling to match second image */
.product-box {
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  width: 100%;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.starting-price {
  font-size: 14px;
  color: #9e3ca2;
  font-weight: 600;
}

.status-badge {
  background-color: #9e3ca2;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

/* UPDATED: Product name styling */
.product-name {
  font-size: 18px;
  font-weight: 700;
  color: #000000;
  margin: 0 0 16px 0;
  line-height: 1.3;
}

.product-details {
  margin-bottom: 16px;
  flex-grow: 1;
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f1f5f9;
}

.price-row:last-child {
  border-bottom: none;
}

.price-row.highlight {
  color: #9e3ca2;
  font-weight: 600;
}

.date-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  margin-top: 8px;
}

.date-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.label {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.value {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
}

.product-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid #f1f5f9;
}

.vendor-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.vendor-name {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
}

.rating {
  display: flex;
  gap: 2px;
}

.star {
  color: #d1d5db;
  font-size: 14px;
}

.star.filled {
  color: #fbbf24;
}

.selected-btn {
  background-color: #9e3ca2;
  color: white;
  border: none;
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
}

/* UPDATED: Right column layout */
.right-column {
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* UPDATED: Header for the right section */
.create-order-header {
  margin-bottom: 20px;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 0;
}

/* UPDATED: Individual order container styling to match left boxes */
.create-order-container {
  width: 100%;
  min-height: 200px;
  padding: 20px;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
}

.create-order-container:last-child {
  margin-bottom: 0;
}

.submit-btn {
  background-color: #9e3ca2;
  border: 1px solid #9e3ca2;
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.submit-btn:hover {
  background-color: #8b2d91;
  border-color: #8b2d91;
}

.selected-products {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.product-card {
  border: none;
  border-radius: 0;
  padding: 0;
  background-color: transparent;
}

.product-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.product-select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
}

/* UPDATED: Selected product display styling */
.selected-product-display {
  font-size: 18px;
  font-weight: 700;
  color: #000000;
  padding: 0;
  background-color: transparent;
  border: none;
  min-height: auto;
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.service-name {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.service-name h3 {
  font-size: 18px;
  color: #1e293b;
  margin: 0;
  text-transform: capitalize;
}

/* UPDATED: Price details styling to match second image */
.price-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  padding: 0;
  border: none;
  margin-bottom: 20px;
}

.price-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.price-label {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.price-input-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.currency {
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
}

.currency.highlight {
  color: #9e3ca2;
}

.price-input {
  width: 120px;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  background-color: white;
}

.price-input.highlight {
  color: #9e3ca2;
  border-color: #9e3ca2;
}

/* UPDATED: Read-only input styling */
.price-input.readonly {
  background-color: #f8fafc;
  color: #64748b;
  cursor: not-allowed;
}

/* UPDATED: Date section styling */
.date-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

.date-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.date-picker {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  background-color: white;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  padding: 24px 24px 0;
  border-bottom: 1px solid #e2e8f0;
}

.modal-header h3 {
  font-size: 20px;
  color: #1e293b;
  font-weight: 700;
  margin: 0;
  padding-bottom: 16px;
}

.modal-body {
  padding: 24px;
}

.modal-body p {
  font-size: 16px;
  color: #64748b;
  margin: 0 0 20px 0;
  line-height: 1.5;
}

.order-summary {
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 20px;
}

/* UPDATED: Service summary styling in modal */
.service-summary {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e2e8f0;
}

.service-summary:last-child {
  margin-bottom: 0;
  border-bottom: none;
  padding-bottom: 0;
}

.service-summary-title {
  font-size: 16px;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 12px 0;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.summary-label {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.summary-value {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
}

.summary-value.highlight {
  color: #9e3ca2;
}

.modal-footer {
  padding: 0 24px 24px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.btn-cancel {
  background-color: white;
  border: 1px solid #d1d5db;
  color: #64748b;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-cancel:hover {
  background-color: #f8fafc;
  border-color: #9ca3af;
}

.btn-confirm {
  background-color: #9e3ca2;
  border: 1px solid #9e3ca2;
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-confirm:hover {
  background-color: #8b2d91;
  border-color: #8b2d91;
}

/* Responsive styles */
@media (max-width: 1024px) {
  .content-layout {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .create-order-container {
    max-width: 100%;
  }
  
  .price-details,
  .date-section {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .main-layout {
    padding: 16px;
  }

  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .product-box {
    width: 100%;
    height: auto;
    min-height: 180px;
  }

  .price-details {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .date-section {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .modal-content {
    margin: 20px;
    width: calc(100% - 40px);
  }
}