import React, { useState } from 'react';
import './CreateOrder.css';
import Header from '../Header-Section/Header';
import { useNavigate, useLocation } from 'react-router-dom';

// Utility functions for in-memory storage (instead of localStorage)
let ordersStorage = [];
const generateOrderId = () => {
  return `ORDER_${Date.now()}`;
};

const saveOrderToStorage = (orderData) => {
  try {
    const newOrder = {
      id: generateOrderId(),
      productName: orderData.selectedProduct,
      startDate: orderData.startDate,
      endDate: orderData.endDate,
      orderStatus: "awarded",
      status: "approved",
      vendor: "ABC Consulting",
      category: mapProductToCategory(orderData.selectedProduct)
    };
    
    ordersStorage.push(newOrder);
    return newOrder;
  } catch (error) {
    console.error('Error saving order:', error);
    return null;
  }
};

const mapProductToCategory = (product) => {
  const categoryMap = {
    'Network Monitoring': 'Network Services',
    'Web Hosting Services': 'Hosting',
    'Enterprise Cloud Hosting': 'Cloud Services',
    'Managed IT Services': 'IT Services',
    'Edge cloud storage': 'Cloud Storage'
  };
  return categoryMap[product] || 'General Services';
};

const CreateOrder = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const passedServices = location.state?.selectedServices || [];
  const [selectedServices, setSelectedServices] = useState(passedServices);
  
  // Create separate state for each service's order details
  const [serviceOrders, setServiceOrders] = useState(() => {
    return passedServices.reduce((acc, service) => {
      acc[service.id] = {
        startingPrice: service.price.replace('SAR ', ''), // Use service's actual price
        offerPrice: '44', // Fixed offer price
        startDate: '',
        endDate: ''
      };
      return acc;
    }, {});
  });
  
  const [showModal, setShowModal] = useState(false);

  const productOptions = [
    'Network Monitoring',
    'Web Hosting Services',
    'Enterprise Cloud Hosting',
    'Managed IT Services',
    'Edge cloud storage'
  ];

  // Update individual service order data
  const updateServiceOrder = (serviceId, field, value) => {
    setServiceOrders(prev => ({
      ...prev,
      [serviceId]: {
        ...prev[serviceId],
        [field]: value
      }
    }));
  };

  const handleSubmit = () => {
    setShowModal(true);
  };

  const handleConfirm = () => {
    // Validate all services have required fields
    const incompleteServices = selectedServices.filter(service => {
      const orderData = serviceOrders[service.id];
      return !orderData.startDate || !orderData.endDate;
    });

    if (incompleteServices.length > 0) {
      alert('Please fill in all required fields for all services');
      return;
    }

    // Create orders for all services
    const savedOrders = [];
    selectedServices.forEach(service => {
      const orderData = {
        selectedProduct: service.service,
        startingPrice: parseFloat(serviceOrders[service.id].startingPrice) || 0,
        offerPrice: parseFloat(serviceOrders[service.id].offerPrice) || 0,
        startDate: serviceOrders[service.id].startDate,
        endDate: serviceOrders[service.id].endDate
      };

      const savedOrder = saveOrderToStorage(orderData);
      if (savedOrder) {
        savedOrders.push(savedOrder);
      }
    });

    if (savedOrders.length === selectedServices.length) {
      console.log('All orders saved successfully:', savedOrders);
      setShowModal(false);
      navigate('/order');
    } else {
      alert('Error creating some orders. Please try again.');
    }
  };

  const handleCancel = () => {
    setShowModal(false);
  };

  return (
    <>
      <Header />
      <div className="main-layout">
        {/* Header */}
        <div className="header">
          <div className="breadcrumb">
            <span className="breadcrumb-purple">Products/</span>
            <span className="breadcrumb-black">Create Order</span>
          </div>
          <div className="header-info">
            <span className="products-selected">{selectedServices.length} Products Selected</span>
          </div>
        </div>

        {/* Two Column Layout */}
        <div className="content-layout">
          {/* Left Column - Selected Products */}
          <div className="left-column">
            <h3 className="section-title">Selected Products</h3>
            
            {selectedServices.map((service, index) => (
              <div key={service.id} className="product-box">
                <div className="product-header">
                  <span className="starting-price">Starting at {service.price}</span>
                </div>
                <h4 className="product-name">{service.service}</h4>
                <div className="product-details">
                  <div className="price-row">
                    <span className="label">Starting Price</span>
                    <span className="value">{service.price}</span>
                  </div>
                  <div className="price-row">
                    <span className="label">Category</span>
                    <span className="value">{service.category}</span>
                  </div>
                </div>
                <div className="product-footer">
                  <div className="vendor-info">
                    <span className="vendor-name">{service.vendor}</span>
                    <div className="rating">
                      {[1, 2, 3, 4, 5].map(star => (
                        <span key={star} className={`star ${star <= service.ratings ? 'filled' : ''}`}>★</span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Right Column - Create Order Containers */}
          <div className="right-column">
            <div className="create-order-header">
              <div className="order-header">
                <div className="breadcrumb"></div>
                <button 
                  className="submit-btn"
                  onClick={handleSubmit}
                >
                  Create All Orders
                </button>
              </div>
            </div>

            {/* Dynamic containers for each selected service */}
            {selectedServices.map((service, index) => (
              <div key={service.id} className="create-order-container">
                <div className="selected-products">
                  <div className="product-card">
                    <div className="product-info">
                      {/* Service Name Display */}
                      <div className="form-group">
                        <div className="selected-product-display">
                          {service.service}
                        </div>
                      </div>

                      {/* Price Details */}
                      <div className="price-details">
                        <div className="price-section">
                          <div className="price-label">
                            Starting Price
                          </div>
                          <div className="price-input-group">
                            <span className="currency">SAR</span>
                            <input
                              type="text"
                              value={serviceOrders[service.id]?.startingPrice || ''}
                              onChange={(e) => {
                                const value = e.target.value;
                                if (value === '' || /^\d*\.?\d*$/.test(value)) {
                                  updateServiceOrder(service.id, 'startingPrice', value);
                                }
                              }}
                              placeholder="0"
                              className="price-input"
                            />
                          </div>
                        </div>

                        <div className="price-section">
                          <div className="price-label">
                            Offer Price
                          </div>
                          <div className="price-input-group">
                            <span className="currency highlight">SAR</span>
                            <input
                              type="text"
                              value={serviceOrders[service.id]?.offerPrice || ''}
                              onChange={(e) => {
                                const value = e.target.value;
                                if (value === '' || /^\d*\.?\d*$/.test(value)) {
                                  updateServiceOrder(service.id, 'offerPrice', value);
                                }
                              }}
                              placeholder="0"
                              className="price-input highlight"
                            />
                          </div>
                        </div>
                      </div>

                      {/* Date Section */}
                      <div className="date-section">
                        <div className="date-group">
                          <label className="form-label">
                            Start Date
                          </label>
                          <input
                            type="date"
                            value={serviceOrders[service.id]?.startDate || ''}
                            onChange={(e) => updateServiceOrder(service.id, 'startDate', e.target.value)}
                            className="date-picker"
                          />
                        </div>
                        <div className="date-group">
                          <label className="form-label">
                            End Date
                          </label>
                          <input
                            type="date"
                            value={serviceOrders[service.id]?.endDate || ''}
                            onChange={(e) => updateServiceOrder(service.id, 'endDate', e.target.value)}
                            className="date-picker"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Confirmation Modal */}
        {showModal && (
          <div className="modal-overlay">
            <div className="modal-content">
              <div className="modal-header">
                <h3>Confirm Order Creation</h3>
              </div>
              <div className="modal-body">
                <p>Are you sure you want to confirm and proceed with creating orders for all selected services?</p>
                <div className="order-summary">
                  {selectedServices.map((service) => (
                    <div key={service.id} className="service-summary">
                      <h4 className="service-summary-title">{service.service}</h4>
                      <div className="summary-item">
                        <span className="summary-label">Starting Price:</span>
                        <span className="summary-value">SAR {serviceOrders[service.id]?.startingPrice || '0'}</span>
                      </div>
                      <div className="summary-item">
                        <span className="summary-label">Offer Price:</span>
                        <span className="summary-value highlight">SAR {serviceOrders[service.id]?.offerPrice || '0'}</span>
                      </div>
                      {serviceOrders[service.id]?.startDate && (
                        <div className="summary-item">
                          <span className="summary-label">Start Date:</span>
                          <span className="summary-value">{serviceOrders[service.id].startDate}</span>
                        </div>
                      )}
                      {serviceOrders[service.id]?.endDate && (
                        <div className="summary-item">
                          <span className="summary-label">End Date:</span>
                          <span className="summary-value">{serviceOrders[service.id].endDate}</span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
              <div className="modal-footer">
                <button 
                  className="btn-cancel"
                  onClick={handleCancel}
                >
                  Cancel
                </button>
                <button 
                  className="btn-confirm"
                  onClick={handleConfirm}
                >
                  Confirm & Proceed
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default CreateOrder;